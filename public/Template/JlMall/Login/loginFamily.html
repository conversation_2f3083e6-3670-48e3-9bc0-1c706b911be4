<!DOCTYPE html>
<html lang="zh-CN" class="ui-page-login">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>家庭卡</title>
    <link rel="stylesheet" href="/ug/mall/css/mui.min.css">
    <link rel="stylesheet" href="/ug/mall/assest/css/iconfont.css">
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
        }
        
        .mui-content {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #F9F9F9;
        }
        
        .register-wrap {
            padding: 0;
            margin: 20px 0 40px;
        }
        
        .register-box {
            padding: 0 15px;
        }
        
        .mui-scroll-wrapper {
            top: 64px;
        }
        
        .register-tips {
            color: #999;
            font-size: 14px;
        }
        
        .register-wrap .mall-input-row>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        .mui-input-row.mall-input-row input {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background-color: #F9F9F9;
        }
        
        .mui-input-row input::-webkit-input-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input::-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-ms-input-placeholder {
            color: #D6D4D4;
        }
        
        .mall-input-row>.mui-input-clear~.mui-icon-clear {
            top: 13px;
        }
        
        .btn-bind {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 24px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #637FFF;
            color: #fff;
        }
        
        .btn-register {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 24px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #F9F9F9;
            color: #637FFF;
        }
        
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        
        .register-box .desc-wrap {
            width: 100%;
            margin: 0 0 10px;
        }
        
        .register-box .desc-wrap p {
            font-size: 16px;
            color: #303133;
            font-weight: 700;
            line-height: 22px;
            margin: 0;
            padding: 0;
        }
        
        .register-box .mall-input-row {
            margin: 0;
            padding: 6px 0;
            background-color: #F9F9F9;
            box-shadow: 0px 1px 0px 0px #EAEAEA;
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
            height: 62px;
            line-height: 48px;
        }
        
        .mall-input-inner {
            -webkit-flex: 1;
            flex: 1;
        }
        
        .flex-box-align-center {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .flex-box-align-between {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .flex-box-align-left {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: flex-start;
            justify-content: flex-start;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .register-box .mall-input-row>span,
        .mall-input-inner>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        #code {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background: #F9F9F9;
        }
        
        .get-code-box {
            width: 80px;
            height: 48px;
            line-height: 48px;
            text-align: center;
        }
        
        .get-code-box>input[type=button] {
            border: 0;
            color: #637FFF;
            height: inherit;
            line-height: inherit;
            font-size: 14px;
            padding: 0;
            font-weight: normal;
            width: 100%;
            border-radius: 0 10px 10px 0;
            background: #F9F9F9;
        }
        
        .bottom-btn {
            width: 100%;
            padding: 0 20px;
        }
        
        .accout-item {
            height: 80px;
            padding: 30px 0 10px;
            border-bottom: 1px solid #EAEAEA;
        }
        
        .item-info {
            flex: 1;
            line-height: 22px;
        }
        
        .item-info .item-name {
            font-size: 16px;
            color: #303133;
            margin: 0;
            padding: 0;
        }
        
        .item-info .item-name em {
            margin-left: 5px;
            font-size: 12px;
            color: #E7B021;
            font-style: normal;
        }
        
        .item-info .item-detail {
            font-size: 14px;
            color: #B3B3B3;
            margin: 3px 0 0;
            padding: 0;
        }
        
        .item-info .item-detail span {
            margin-right: 20px;
        }
        
        .item-tag {
            height: 22px;
            line-height: 22px;
            font-size: 12px;
            color: #8EA2F9;
            padding: 0 8px;
            border: 1px solid #D8D8D8;
            border-radius: 11px;
        }
        
        .item-tag.main {
            border: 0;
            font-size: 14px;
        }
        
        .item-tag.no {
            color: #D8D8D8;
        }
        
        .item-tag .mui-icon {
            font-size: 12px;
            color: #D8D8D8;
        }
        
        .radio-box {
            width: 37px;
            padding: 0 11px;
        }
        
        .mui-checkbox.mui-left input[type=checkbox],
        .mui-radio.mui-left input[type=radio] {
            width: 15px;
            height: 15px;
            left: 6px;
        }
        
        .mui-checkbox input[type=checkbox]:before,
        .mui-radio input[type=radio]:before {
            font-family: Muiicons;
            font-size: 18px;
        }
        
        .mui-checkbox input[type=checkbox]:checked:before,
        .mui-radio input[type=radio]:checked:before {
            color: #637FFF;
        }
        
        .header-tab {
            width: 100%;
            height: 44px;
            background: #fff;
            font-size: 14px;
            color: #919398;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
        }
        
        .tab-item span {
            line-height: 20px;
            padding: 12px 3px;
        }
        
        .tab-item.active span {
            color: #637FFF;
            border-bottom: 3px solid #637FFF;
        }
        
        .tag-popover {
            width: 100%;
            height: 173px;
            background: #fff;
            bottom: 0;
            border-radius: 0;
        }
        
        .tag-list {
            width: 100%;
            margin-top: 69px;
            padding: 0 30px;
        }
        
        .tag-list li {
            width: 90px;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            color: #303133;
            border: 1px solid #C0C4CC;
            border-radius: 4px;
            text-align: center;
            list-style: none;
        }
        
        .tag-list li.active {
            color: #fff;
            border: 1px solid #637FFF;
            background: #637FFF;
        }
        
        .main-wrap {
            width: 100%;
        }
        
        .empty-wrap {
            width: 100%;
            height: 100%;
        }
        
        .empty-wrap .empty-img {
            width: 135px;
            height: 103px;
            overflow: hidden;
            margin: 50px auto 0;
        }
        
        .empty-wrap .empty-img img {
            width: 100%;
            height: 100%;
        }
        
        .empty-wrap .empty-info {
            line-height: 20px;
            font-size: 14px;
            color: #919398;
            text-align: center;
            margin: 10px auto;
        }
    </style>

</head>

<body>
    <div class="mui-content mall-login">
        <div class="header-tab flex-box-align-center">
            <div class="tab-item" _w='0'><span>绑定</span></div>
            <div class="tab-item active" _w='1'><span>家庭主副卡</span></div>
            <div class="tab-item" _w='2'><span>合并账号</span></div>
        </div>
        <div class="empty-wrap mui-hidden">
            <div class="empty-img"><img src="/ug/mall/assest/images/empty-accout.png"></div>
            <p class="empty-info">暂时没有可创建家庭主副卡的账号<br/>您可以直接去绑定</p>
        </div>
        <div class="main-wrap">
            <div class="register-wrap">
                <div class="mui-scroll-wrapper">
                    <div class="mui-scroll">
                        <div class="register-box">

                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom-btn">
                <div class="mui-btn-primary btn-bind">创建家庭</div>
            </div>
        </div>
        <div id="tagPopover" class="mui-popover tag-popover">
            <ul class="tag-list flex-box-align-between">
                <li class="list-item" _w="1"><label>家庭主卡</label></li>
                <li class="list-item active" _w="2"><label>家庭副卡</label></li>
                <li class="list-item" _w="0"><label>不选择</label></li>
            </ul>
        </div>
    </div>
</body>
<script src="/ug/js/jquery.min.js"></script>
<script src="/ug/mall/js/mui.min.js"></script>
<script src="/ug/mall/js/mui.picker.min.js"></script>
<script src="/ug/mall/assest/js/mui.utils.js"></script>
<script>
    var logininfo = "#{$user_info}#";
    var tabUrl = "#{$tabUrl}#";
    var flag = "{$flag}";
    console.log(flag)

    //-- beg
    flag = true;
    logininfo = {
        userinfo: {
            mobile: 13221011234
        },
        main_data: [{
            id: 1,
            name: '蒙娜丽莎',
            sex: '女',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }],
        data: [{
            id: 2,
            name: '张三',
            sex: '男',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }, {
            id: 3,
            name: '蒙娜丽莎',
            sex: '女',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }, {
            id: 4,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 5,

            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 6,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 6,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }]
    };
    //-- end
    var INDEX_ARR = [],
        TAP_INDEX = 0,
        MAIN_INDEX = null;

    $(function() {
        if (flag) {
            htmlInfo();
        } else {
            $('.empty-wrap').removeClass('mui-hidden');
            $('.main-wrap').addClass('mui-hidden');
        }

        mui('.header-tab').on('tap', '.tab-item', function() {
            var no = parseInt($(this).attr('_w'));
            window.location.replace(tabUrl[no]);
        });

        mui('.register-box').on('tap', '.accout-item', function(event) {
            var no = $(this).attr('_no'),
                checked = $(this).find('input').prop('checked'),
                index = INDEX_ARR.indexOf(no);
            if ($(event.target).closest('div.item-tag').length > 0) {
                return;
            }
            if (index >= 0) {
                INDEX_ARR.splice(index, 1);
                if (event.target.type != 'checkbox') {
                    $(this).find('input').prop('checked', false);
                }
            } else {
                var text = $(this).find('.item-tag>label').html();
                if (text == '不选择') {
                    mui.toast('请先设置账号属性');
                    if (event.target.type == 'checkbox') {
                        $(this).find('input').prop('checked', true);
                    }
                    return;
                }
                INDEX_ARR.push(no);
                if (event.target.type != 'checkbox') {
                    $(this).find('input').prop('checked', !checked);
                }
            }
        });

        mui('.register-box').on('tap', '.item-tag', function() {
            if ($(this).closest('div.accout-item').attr('_w') == 'main') {
                return;
            }
            var no = parseInt($(this).closest('div.accout-item').attr('_no')),
                text = $(this).find('label').html();
            TAP_INDEX = no;
            if (text == '不选择') {
                $('#tagPopover').find('li[_w=0]').addClass('active').siblings().removeClass('active');
            }
            if (text == '家庭主卡') {
                $('#tagPopover').find('li[_w=1]').addClass('active').siblings().removeClass('active');
            }
            if (text == '家庭副卡') {
                $('#tagPopover').find('li[_w=2]').addClass('active').siblings().removeClass('active');
            }
            mui('#tagPopover').popover('show');
        });

        mui('#tagPopover').on('tap', 'li.list-item', function() {
            var no = parseInt($(this).attr('_w')),
                item = $('.register-box').find('div.accout-item[_no=' + TAP_INDEX + ']').find('.item-tag');
            if ($(this).hasClass('active')) {
                return;
            } else {
                $(this).addClass('active').siblings().removeClass('active');
            }
            if (no == 0) {
                item.find('label').html('不选择');
                item.addClass('no');
            } else if (no == 1) {
                if (logininfo.main_data.length > 0 || (MAIN_INDEX != null && MAIN_INDEX != TAP_INDEX)) {
                    mui.toast('已有家庭主卡');
                    return;
                }
                MAIN_INDEX = TAP_INDEX;
                item.find('label').html('家庭主卡');
                item.removeClass('no');
            } else if (no == 2) {
                item.find('label').html('家庭副卡');
                item.removeClass('no');
            }
            mui('#tagPopover').popover('hide');
        });
        mui('.bottom-btn').on('tap', '.btn-bind', submitInfo);
    });

    function htmlInfo() {
        var html = '',
            userinfo = logininfo.userinfo,
            i = 0,
            item, birthday;
        html += '<div class="desc-wrap">';
        html += '	<p>根据' + userinfo.mobile + '匹配到以下几个账号</p>';
        html += '	<p>您可以创建“我的家庭”主卡和副卡</p>';
        html += '</div>';
        html += '<div class="list-wrap">';
        if (logininfo.main_data.length > 0) {
            item = logininfo.main_data[0];
            birthday = item.birthday ? item.birthday.date('yyyy-MM-dd') : '--';
            html += '   <div class="accout-item flex-box-align-left" _w="main">';
            html += '		<div class="item-info">';
            html += '		     <h5 class="item-name">' + item.name + '<em>' + item.grade + '</em></h5>';
            html += '		     <p class="item-detail"><span>' + item.sex + '</span><span>生日 ' + birthday + '</span></p>';
            html += '		</div>';
            html += '		<div class="item-tag">';
            html += '		     <label>家庭主卡</label><span class="mui-icon mui-icon-arrowdown"></span>';
            html += '		</div>';
            html += '	</div>';
            //todo 将创建家庭按钮改为加入家庭
        }
        for (i = 0; i < logininfo.data.length; i++) {
            item = logininfo.data[i];
            birthday = item.birthday ? item.birthday.date('yyyy-MM-dd') : '--';
            html += '   <div class="accout-item flex-box-align-left" _no="' + i + '">';
            html += '		<div class="mui-input-row mui-checkbox mui-left radio-box ' + ((i == 0) ? 'checked' : '') + '">';
            html += '			<label></label>';
            html += '			<input class="hg-check-col flag-check-item" type="checkbox" value="">';
            html += '		</div>';
            html += '		<div class="item-info">';
            html += '		     <h5 class="item-name">' + item.name + '<em>' + item.grade + '</em></h5>';
            html += '		     <p class="item-detail"><span>' + item.sex + '</span><span>生日 ' + birthday + '</span></p>';
            html += '		</div>';
            html += '		<div class="item-tag no">';
            html += '		     <label>不选择</label><span class="mui-icon mui-icon-arrowdown"></span>';
            html += '		</div>';
            html += '	</div>';
        }
        html += '</div>';
        $('.register-box').html(html);

        var sHeight = $('.mui-content').height() - 64 - 108,
            listHeight = $('.register-box').height();
        if (listHeight < sHeight) {
            $('.register-wrap').height(listHeight);
            $('.mui-scroll-wrapper').height(listHeight);
        } else {
            $('.register-wrap').height(sHeight);
            $('.mui-scroll-wrapper').height(sHeight);
            mui('.mui-scroll-wrapper').scroll();
        }
    }

    function submitInfo() {
        var turl = "{$url.addFamilyCard}",
            pdata = {},
            mainId,
            subsId = [];
        if (logininfo.main_data.length == 0 && MAIN_INDEX == null) {
            mui.toast('请先选择家庭主卡');
            return;
        }
        mainId = MAIN_INDEX == null ? logininfo.main_data[0].id : logininfo.data[MAIN_INDEX].id;
        for (var i = 0; i < INDEX_ARR.length; i++) {
            var el = $('.register-box').find('div.accout-item[_no=' + INDEX_ARR[i] + ']'),
                text = el.find('.item-tag>label').html();
            if (text == '家庭副卡') {
                subsId.push(logininfo.data[INDEX_ARR[i]].id);
            }
        }
        pdata = {
            main: mainId,
            subs: subsId
        };
        console.log(pdata);
        console.log(turl)
            // return;
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                window.location.reload();
            } else {
                mui.alert(jo.getMessage());
            }
        });
    }
</script>

</html>