<!DOCTYPE html>
<html lang="zh-CN" class="ui-page-login">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>合并</title>
    <link rel="stylesheet" href="/ug/mall/css/mui.min.css">
    <link rel="stylesheet" href="/ug/mall/assest/css/iconfont.css">
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
        }
        
        .mui-content {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #F9F9F9;
        }
        
        .register-wrap {
            padding: 0;
            margin: 20px 0 40px;
        }
        
        .register-box {
            padding: 0 15px;
        }
        
        .mui-scroll-wrapper {
            top: 176px;
        }
        
        .register-tips {
            color: #999;
            font-size: 14px;
        }
        
        .register-wrap .mall-input-row>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        .mui-input-row.mall-input-row input {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background-color: #F9F9F9;
        }
        
        .mui-input-row input::-webkit-input-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input::-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-ms-input-placeholder {
            color: #D6D4D4;
        }
        
        .mall-input-row>.mui-input-clear~.mui-icon-clear {
            top: 13px;
        }
        
        .btn-bind {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 24px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #637FFF;
            color: #fff;
        }
        
        .btn-register {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 24px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #F9F9F9;
            color: #637FFF;
        }
        
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        
        .register-box .desc-wrap {
            width: 100%;
            margin: 0 0 10px;
        }
        
        .register-box .desc-wrap p {
            font-size: 16px;
            color: #303133;
            font-weight: 700;
            line-height: 22px;
            margin: 0;
            padding: 0;
        }
        
        .register-box .mall-input-row {
            margin: 0;
            padding: 6px 0;
            background-color: #F9F9F9;
            box-shadow: 0px 1px 0px 0px #EAEAEA;
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
            height: 62px;
            line-height: 48px;
        }
        
        .mall-input-inner {
            -webkit-flex: 1;
            flex: 1;
        }
        
        .flex-box-align-center {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .flex-box-align-between {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .flex-box-align-left {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: flex-start;
            justify-content: flex-start;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .register-box .mall-input-row>span,
        .mall-input-inner>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        #code {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background: #F9F9F9;
        }
        
        .get-code-box {
            width: 80px;
            height: 48px;
            line-height: 48px;
            text-align: center;
        }
        
        .get-code-box>input[type=button] {
            border: 0;
            color: #637FFF;
            height: inherit;
            line-height: inherit;
            font-size: 14px;
            padding: 0;
            font-weight: normal;
            width: 100%;
            border-radius: 0 10px 10px 0;
            background: #F9F9F9;
        }
        
        .bottom-btn {
            width: 100%;
            padding: 0 20px;
        }
        
        .accout-item {
            height: 80px;
            padding: 30px 0 10px;
            border-bottom: 1px solid #EAEAEA;
        }
        
        .item-info {
            flex: 1;
            line-height: 22px;
        }
        
        .item-info .item-name {
            font-size: 16px;
            color: #303133;
            margin: 0;
            padding: 0;
        }
        
        .item-info .item-name em {
            margin-left: 5px;
            font-size: 12px;
            color: #E7B021;
            font-style: normal;
        }
        
        .item-info .item-detail {
            font-size: 14px;
            color: #B3B3B3;
            margin: 3px 0 0;
            padding: 0;
        }
        
        .item-info .item-detail span {
            margin-right: 20px;
        }
        
        .radio-box {
            width: 37px;
            padding: 0 11px;
        }
        
        .mui-checkbox.mui-left input[type=checkbox],
        .mui-radio.mui-left input[type=radio] {
            width: 15px;
            height: 15px;
            left: 6px;
        }
        
        .mui-checkbox input[type=checkbox]:before,
        .mui-radio input[type=radio]:before {
            font-family: Muiicons;
            font-size: 18px;
        }
        
        .mui-checkbox input[type=checkbox]:checked:before,
        .mui-radio input[type=radio]:checked:before {
            color: #637FFF;
        }
        
        .header-tab {
            width: 100%;
            height: 44px;
            background: #fff;
            font-size: 14px;
            color: #919398;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
        }
        
        .tab-item span {
            line-height: 20px;
            padding: 12px 3px;
        }
        
        .tab-item.active span {
            color: #637FFF;
            border-bottom: 3px solid #637FFF;
        }
        
        .first-wrap {
            height: 112px;
            padding: 20px 15px 0;
        }
        
        .first-wrap h5.title {
            font-size: 16px;
            line-height: 20px;
            color: #303133;
            font-weight: 700;
            margin: 0;
            padding: 0;
        }
        
        .first-wrap .account-wrap {
            font-size: 14px;
            line-height: 20px;
            padding: 20px 0 10px;
            border-bottom: 1px solid #D6D4D4;
        }
        
        .first-wrap .mui-icon {
            width: 16px;
            font-size: 16px;
            color: #D6D4D4;
        }
        
        .first-wrap .item-info .empty {
            color: #D6D4D4;
            padding: 0;
            margin: 0;
            line-height: 41px;
        }
        
        .user-popover {
            width: 100%;
            height: 323px;
            background: #fff;
            bottom: 0;
            border-radius: 0;
        }
        
        .user-list {
            width: 100%;
            margin: 0;
            padding: 0 15px;
        }
        
        .popover-btn {
            position: absolute;
            width: 100%;
            bottom: 0;
        }
        
        .btn-save {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #637FFF;
            color: #fff;
        }
        
        .mui-popover .mui-scroll-wrapper {
            top: 0;
            margin: 0 0 58px;
        }
        
        .main-wrap {
            width: 100%;
        }
        
        .empty-wrap {
            width: 100%;
            height: 100%;
        }
        
        .empty-wrap .empty-img {
            width: 135px;
            height: 103px;
            overflow: hidden;
            margin: 50px auto 0;
        }
        
        .empty-wrap .empty-img img {
            width: 100%;
            height: 100%;
        }
        
        .empty-wrap .empty-info {
            line-height: 20px;
            font-size: 14px;
            color: #919398;
            text-align: center;
            margin: 10px auto;
        }
    </style>

</head>

<body>
    <div class="mui-content mall-login">
        <div class="header-tab flex-box-align-center">
            <div class="tab-item" _w='0'><span>绑定</span></div>
            <div class="tab-item" _w='1'><span>家庭主副卡</span></div>
            <div class="tab-item active" _w='2'><span>合并账号</span></div>
        </div>
        <div class="empty-wrap mui-hidden">
            <div class="empty-img"><img src="/ug/mall/assest/images/empty-accout.png"></div>
            <p class="empty-info">暂时没有可合并的账号<br/>您可以直接去绑定</p>
        </div>
        <div class="main-wrap">
            <div class="first-wrap">
                <h5 class="title">选择保留账号</h5>
                <div class="account-wrap flex-box-align-between">
                    <div class="item-info" id="saveInfo">
                        <p class="empty">请选择保留账号</p>
                        <!--  <h5 class="item-name">蒙娜丽莎<em>银卡会员</em></h5>
                    <p class="item-detail"><span>女</span><span>生日 1990-10-11</span></p> -->
                    </div>
                    <span class="mui-icon mui-icon-arrowright"></span>
                </div>
            </div>
            <div class="register-wrap">
                <div class="mui-scroll-wrapper login-list">
                    <div class="mui-scroll">
                        <div class="register-box">

                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom-btn">
                <div class="mui-btn-primary btn-bind">合并</div>
            </div>
        </div>
        <div id="userPopover" class="mui-popover user-popover">
            <div class="mui-scroll-wrapper">
                <div class="mui-scroll">
                    <div class="user-list">
                        <div class="accout-item flex-box-align-left" _no="0">
                            <div class="mui-input-row mui-checkbox mui-left radio-box">
                                <label></label>
                                <input class="hg-check-col flag-check-item" type="checkbox" value="">
                            </div>
                            <div class="item-info">
                                <h5 class="item-name">蒙娜丽莎<em>银卡会员</em></h5>
                                <p class="item-detail"><span>女</span><span>生日 1990-10-11</span></p>
                            </div>
                        </div>
                        <div class="accout-item flex-box-align-left" _no="1">
                            <div class="mui-input-row mui-checkbox mui-left radio-box">
                                <label></label>
                                <input class="hg-check-col flag-check-item" type="checkbox" value="">
                            </div>
                            <div class="item-info">
                                <h5 class="item-name">蒙娜丽莎<em>银卡会员</em></h5>
                                <p class="item-detail"><span>女</span><span>生日 1990-10-11</span></p>
                            </div>
                        </div>
                        <div class="accout-item flex-box-align-left" _no="2">
                            <div class="mui-input-row mui-checkbox mui-left radio-box">
                                <label></label>
                                <input class="hg-check-col flag-check-item" type="checkbox" value="">
                            </div>
                            <div class="item-info">
                                <h5 class="item-name">蒙娜丽莎<em>银卡会员</em></h5>
                                <p class="item-detail"><span>女</span><span>生日 1990-10-11</span></p>
                            </div>
                        </div>
                        <div class="accout-item flex-box-align-left" _no="2">
                            <div class="mui-input-row mui-checkbox mui-left radio-box">
                                <label></label>
                                <input class="hg-check-col flag-check-item" type="checkbox" value="">
                            </div>
                            <div class="item-info">
                                <h5 class="item-name">蒙娜丽莎<em>银卡会员</em></h5>
                                <p class="item-detail"><span>女</span><span>生日 1990-10-11</span></p>
                            </div>
                        </div>
                        <div class="accout-item flex-box-align-left" _no="2">
                            <div class="mui-input-row mui-checkbox mui-left radio-box">
                                <label></label>
                                <input class="hg-check-col flag-check-item" type="checkbox" value="">
                            </div>
                            <div class="item-info">
                                <h5 class="item-name">蒙娜丽莎<em>银卡会员</em></h5>
                                <p class="item-detail"><span>女</span><span>生日 1990-10-11</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="popover-btn">
                <div class="mui-btn-primary btn-save">确定</div>
            </div>
        </div>
    </div>
</body>
<script src="/ug/js/jquery.min.js"></script>
<script src="/ug/mall/js/mui.min.js"></script>
<script src="/ug/mall/assest/js/mui.utils.js"></script>
<script>
    var logininfo = "#{$user_info}#";
    var can_choose = "#{$can_choose_list}#";
    var tabUrl = "#{$tabUrl}#";
    var flag = "{$flag}";

    //-- beg
    flag = false;
    logininfo = {
        data: [{
            id: 1,
            name: '张三',
            sex: '男',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }, {
            id: 2,
            name: '蒙娜丽莎',
            sex: '女',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }, {
            id: 3,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 4,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 5,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 6,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 7,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }]
    };
    can_choose: [{
            id: 1,
            name: '张三',
            sex: '男',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }, {
            id: 2,
            name: '蒙娜丽莎',
            sex: '女',
            grade: '金卡会员',
            birthday: '1990-01-01'
        }, {
            id: 3,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 4,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 5,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 6,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }, {
            id: 7,
            name: '李二',
            sex: '男',
            grade: '银卡会员',
            birthday: '1990-01-01'
        }]
        //-- end
    var INDEX_ARR = [],
        SAVE_INDEX = null,
        userData = logininfo.data;
    $(function() {
        if (flag) {
            htmlInfo();
            htmlPopover();
            mui('.mui-scroll-wrapper').scroll();
        } else {
            $('.empty-wrap').removeClass('mui-hidden');
            $('.main-wrap').addClass('mui-hidden');
        }

        mui('.first-wrap').on('tap', '.account-wrap', function() {
            $('#userPopover').find('div.accout-item').each(function() {
                $(this).find('input').prop('checked', false);
            });
            if (SAVE_INDEX != null) {
                SAVE_INDEX = parseInt($('#saveInfo').find('h5.item-name').attr('_no'));
                $('#userPopover').find('.accout-item').eq(SAVE_INDEX).find('input').prop('checked', true);
            }
            mui('#userPopover').popover('show');
        });
        mui('#userPopover').on('tap', '.btn-save', function() {
            SAVE_INDEX = null;
            $('.user-list').find('input[type=checkbox]').each(function() {
                if ($(this).prop('checked') == true) {
                    SAVE_INDEX = parseInt($(this).closest('div.accout-item').attr('_no'));
                    return;
                }
            });
            if (SAVE_INDEX != null) {
                var html = '',
                    item = userData[SAVE_INDEX],
                    birthday = item.birthday ? item.birthday.date('yyyy-MM-dd') : '--';
                html += '<h5 class="item-name" _no="' + SAVE_INDEX + '">' + item.name + '<em>' + item.grade + '</em></h5>';
                html += '<p class="item-detail"><span>' + item.sex + '</span><span>生日 ' + birthday + '</span></p>';
                var turl = tabUrl[3]
                pdata = {
                    keep_client_id: item.id
                };
                $.ajaxExt(turl, pdata, function(jo) {
                    if (jo.isOk()) {
                        can_choose = jo.can_choose_list;
                        htmlInfo();
                    } else {
                        mui.alert(jo.getMessage());
                    }
                });
            } else {
                html += '<p class="empty">请选择保留账号</p>';
            }
            $('#saveInfo').html(html);
            mui('#userPopover').popover('hide');
        });
        mui('.header-tab').on('tap', '.tab-item', function() {
            var no = parseInt($(this).attr('_w'));
            window.location.replace(tabUrl[no]);
        });
        mui('.user-list').on('tap', '.accout-item', function(event) {
            var no = $(this).attr('_no'),
                checked = $(this).find('input').prop('checked');
            if (no == SAVE_INDEX) {
                if (event.target.type == 'checkbox') {
                    $(this).find('input').prop('checked', false);
                } else {
                    $(this).find('input').prop('checked', true);
                }
            } else {
                SAVE_INDEX = no;
                if (event.target.type == 'checkbox') {} else {
                    $(this).find('input').prop('checked', !checked);
                }
                $(this).siblings('div.accout-item').each(function() {
                    $(this).find('input').prop('checked', false);
                });
            }
        });
        mui('.register-box').on('tap', '.accout-item', function(event) {
            var no = $(this).attr('_no'),
                checked = $(this).find('input').prop('checked'),
                index = INDEX_ARR.indexOf(no);
            if (index >= 0) {
                INDEX_ARR.splice(index, 1);
                if (event.target.type != 'checkbox') {
                    $(this).find('input').prop('checked', false);
                }
            } else {
                INDEX_ARR.push(no);
                if (event.target.type != 'checkbox') {
                    $(this).find('input').prop('checked', !checked);
                }
            }
        });
        mui('.bottom-btn').on('tap', '.btn-bind', submitInfo);
    });

    function htmlPopover() {
        var html = '',
            i = 0;
        for (i = 0; i < userData.length; i++) {
            var item = userData[i],
                birthday = item.birthday ? item.birthday.date('yyyy-MM-dd') : '--';
            html += '   <div class="accout-item flex-box-align-left" _no="' + i + '">';
            html += '		<div class="mui-input-row mui-checkbox mui-left radio-box ' + ((i == 0) ? 'checked' : '') + '">';
            html += '			<label></label>';
            html += '			<input class="hg-check-col flag-check-item" type="checkbox" value="">';
            html += '		</div>';
            html += '		<div class="item-info">';
            html += '		     <h5 class="item-name">' + item.name + '<em>' + item.grade + '</em></h5>';
            html += '		     <p class="item-detail"><span>' + item.sex + '</span><span>生日 ' + birthday + '</span></p>';
            html += '		</div>';
            html += '	</div>';
        }
        $('.user-list').html(html);
    }

    function htmlInfo() {
        var html = '',
            i = 0;
        html += '<div class="list-wrap">';
        for (i = 0; i < can_choose.length; i++) {
            var item = can_choose[i],
                birthday = item.birthday ? item.birthday.date('yyyy-MM-dd') : '--';
            html += '   <div class="accout-item flex-box-align-left" _no="' + i + '">';
            html += '		<div class="mui-input-row mui-checkbox mui-left radio-box ' + ((i == 0) ? 'checked' : '') + '">';
            html += '			<label></label>';
            html += '			<input class="hg-check-col flag-check-item" type="checkbox" value="">';
            html += '		</div>';
            html += '		<div class="item-info">';
            html += '		     <h5 class="item-name">' + item.name + '<em>' + item.grade + '</em></h5>';
            html += '		     <p class="item-detail"><span>' + item.sex + '</span><span>生日 ' + birthday + '</span></p>';
            html += '		</div>';
            html += '	</div>';
        }
        html += '</div>';
        $('.register-box').html(html);
        var sHeight = $('.mui-content').height() - 176 - 108,
            listHeight = $('.register-box').height();
        if (listHeight < sHeight) {
            $('.register-wrap').height(listHeight);
            $('.register-box').closest('.mui-scroll-wrapper').height(listHeight);
        } else {
            $('.register-wrap').height(sHeight);
            $('.register-box').closest('.mui-scroll-wrapper').height(sHeight);
        }
    }

    function submitInfo() {
        if ($('#saveInfo').find('h5').length == 0) {
            mui.toast('请先选择保留账号');
            return;
        }
        var mainIndex = parseInt($('#saveInfo').find('h5.item-name').attr('_no')),
            turl = "{$url.doMerge}",
            pdata = {},
            saveId = '';
        for (var i = 0; i < INDEX_ARR.length; i++) {
            var item = INDEX_ARR[i];
            saveId += can_choose[item].id;
            if (i != INDEX_ARR.length - 1) {
                saveId += ',';
            }
        }
        pdata = {
            keep_client_id: userData[mainIndex].id,
            del_client_ids: saveId
        }
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                window.location.reload();
            } else {
                mui.alert(jo.getMessage());
            }
        });
    }
</script>

</html>