<!DOCTYPE html>
<html lang="zh-CN" class="ui-page-login">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>绑定</title>
    <link rel="stylesheet" href="/ug/mall/css/mui.min.css">
    <link rel="stylesheet" href="/ug/mall/assest/css/iconfont.css">
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
        }

        .mui-content {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #fff;
        }

        .mall-login {
            padding: 0 15px;
        }

        .mall-login-box {
            /*padding-bottom: 30px;*/
            padding: 76px 0 0;
        }

        .mall-login-tips {
            color: #999;
            font-size: 14px;
        }

        .mall-login .mall-input-row>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }

        .mui-input-row.mall-input-row input {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
        }

        .mui-input-row input::-webkit-input-placeholder {
            color: #D6D4D4;
        }

        .mui-input-row input::-moz-placeholder {
            color: #D6D4D4;
        }

        .mui-input-row input:-moz-placeholder {
            color: #D6D4D4;
        }

        .mui-input-row input:-ms-input-placeholder {
            color: #D6D4D4;
        }

        .mall-input-row>.mui-input-clear~.mui-icon-clear {
            top: 13px;
        }

        .btn-sign-in {
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            margin-top: 30px;
            border-radius: 24px;
            text-align: center;
            border: #637FFF;
            background-color: #637FFF;
            color: #fff;
        }

        .btn-sign-in,
        .btn-register {
            text-align: center;
        }

        .btn-register {
            margin-top: 20px;
        }

        .btn-register>a {
            padding: 5px 6px;
            font-size: 16px;
            color: #7D95FF;
        }

        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }

        .mall-login-box h5.title {
            margin: 0;
            font-size: 20px;
            color: #303133;
            font-weight: 700;
            line-height: 28px;
        }

        .mall-login-box .mall-input-row {
            margin: 0;
            padding: 6px 0;
            background-color: rgb(255, 255, 255);
            border-bottom: 1px solid #EAEAEA;
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
            height: 62px;
            line-height: 48px;
        }

        .mall-input-inner {
            -webkit-flex: 1;
            flex: 1;
        }

        .flex-box-align-center {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
        }

        .register-box .mall-input-row>span,
        .mall-input-inner>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }

        #code {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
        }

        .get-code-box {
            width: 80px;
            height: 48px;
            line-height: 48px;
            text-align: center;
        }

        .get-code-box>input[type=button] {
            border: 0;
            color: #637FFF;
            height: inherit;
            line-height: inherit;
            font-size: 14px;
            padding: 0;
            font-weight: normal;
            width: 100%;
            border-radius: 0 10px 10px 0;
        }

        .bottom-btn {
            width: 100%;
            padding: 0 20px;
        }
    </style>

</head>

<body>
    <div class="mui-content mall-login">
        <div class="mall-login-wrap">
            <div class="mall-login-box">
                <h5 class="title">绑定</h5>
                <!--                <div class="mui-input-row mall-input-row">-->
                <!--                    <input id="account" class="mui-input-clear mui-input" type="text" placeholder="请输入用户名">-->
                <!--                </div>-->
                <div class="mui-input-row mall-input-row">
                    <input id="mobile" class="mui-input-clear mui-input" type="number" pattern="[0-9]*"
                        placeholder="请输入手机号">
                </div>
                <div class="mall-input-row">
                    <div class="mui-input-row flex-box-align-center mall-input-inner">
                        <input id="code" class="mui-input-clear mui-input" type="text" placeholder="请输入验证码">
                    </div>
                    <div class="get-code-box">
                        <input class="btn-get-code" type="button" value="获取验证码" />
                    </div>
                </div>
            </div>
            <!-- 用户协议同意区域 -->
            <div class="agreement-box"
                style="margin: 20px 0; display: flex; align-items: center; font-size: 14px; color: #999;">
                <input type="checkbox" id="agreement-checkbox" style="margin-right: 8px; transform: scale(1.2);">
                <label for="agreement-checkbox" style="cursor: pointer;">
                    已阅读并同意以下协议：
                    <!-- <a href="#" onclick="showUserAgreement()"
                        style="color: #637FFF; text-decoration: none;">《用户服务协议》</a> -->
                    <a href="#" onclick="showPrivacyPolicy()" style="color: #637FFF; text-decoration: none;">《隐私政策》</a>
                </label>
            </div>
            <div class="bottom-btn">
                <div class="mui-btn-primary btn-sign-in">绑定</div>
                <div class="btn-register">
                    <a href="{$url.register}">新用户注册</a>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="/ug/js/jquery.min.js"></script>
<script src="/ug/mall/js/mui.min.js"></script>
<script src="/ug/mall/assest/js/mui.utils.js"></script>
<script>
    $(function () {
        mui('.mall-login').on('tap', '.btn-sign-in', signIn);
        mui('.mall-login').on('tap', '.btn-register>a', function () {
            window.location.href = this.href;
        });
        $('.mall-login-box input').on('blur', function () {
            document.activeElement.blur();
        });
        mui('.mall-login-wrap').on('tap', '.get-code-box > .btn-get-code', setCode);
    });

    function signIn() {
        var account = $('#account').val(),
            mobile = $('#mobile').val(),
            code = $('#code').val();
        document.activeElement.blur();

        /*验证用户信息是否为空*/
        if (!check({
            mobile: mobile,
            code: code
        })) return false;
        var turl = "";
        pdata = {
            uname: account,
            phone: mobile,
            ver_code: code
        };
        $.ajaxExt(turl, pdata, function (jo) {
            if (jo._c == 0) {
                window.location.replace('{$url.redirect}');
            } else if (jo._c == 1) {
                mui.toast(jo._m);
            } else if (jo._c == 2) {
                window.location.replace('{$url.loginlist}' + '?phone=' + mobile);
            } else {
                mui.toast(jo._m);
            }
        });
    }

    var wait = 60;

    function time(btn) {
        if (wait == 0) {
            btn.removeClass("bg-gray");
            btn.removeAttr("disabled");
            btn.val("获取验证码");
            wait = 60;
        } else {
            btn.addClass("bg-gray");
            btn.attr("disabled", true);
            btn.val(wait + "秒重发");
            wait--;
            setTimeout(function () {
                time(btn);
            }, 1000);
        }
    };

    function isEmpty(val) {
        return val === ''
    };

    function check_userName(account) {
        var _max = 8;
        var _cur = getByteLen(account);
        if (_cur > _max) {
            return false;
        }
        return true;
    };

    function getByteLen(val) { // 返回val的字节长度
        var len = 0;
        for (var i = 0; i < val.length; i++) {
            /*if(val[i].match(/[^\x00-\xff]/ig) != null) //全角
                len += 2;
            else*/
            len += 1;
        }
        return len;
    };

    function check(obj) {
        // if (isEmpty(obj.account)) {
        //     mui.toast('用户名不能为空');
        //     return false;
        // };
        //				if(!check_userName(obj.account)) {
        //					mui.toast('用户名过长');
        //					return false;
        //				};
        if (isEmpty(obj.mobile)) {
            mui.toast('手机号不能为空');
            return false;
        };
        if (!isValidPhone(obj.mobile)) {
            mui.toast('请填写正确的手机号');
            return false;
        }
        if (isEmpty(obj.code)) {
            mui.toast('验证码不能为空');
            return false;
        }
        // 检查是否同意用户协议
        if (!$('#agreement-checkbox').is(':checked')) {
            mui.toast('请先同意用户服务协议和隐私政策');
            return false;
        }
        return true
    };

    function isValidPhone(val) { //手机号校验
        return /^1[0-9]{10}$/.test(val);
    };

    function setCode() {
        document.activeElement.blur();
        var mobile = $('#mobile').val();
        /*验证用户信息是否为空*/
        if (isEmpty(mobile)) {
            mui.toast('手机号不能为空');
            return false;
        };
        if (!isValidPhone(mobile)) {
            mui.toast('请填写正确的手机号');
            return false;
        }
        var turl = '{$url.getcode}';
        var pdata = {
            'mobile': mobile
        }
        time($('.btn-get-code'));
        $.ajaxExt(turl, pdata, function (jo) {
            if (jo.isOk()) {
                mui.toast(jo.getMessage());
            } else {
                mui.alert(jo.getMessage());
            }
        });
    };

    // 显示用户服务协议
    function showUserAgreement() {
        mui.alert('用户服务协议内容...', '用户服务协议', function () {
            // 可以在这里添加跳转到详细协议页面的逻辑
            // window.open('/agreement/user-service', '_blank');
        });
    }

    // 显示隐私政策
    function showPrivacyPolicy() {
        mui.alert('隐私政策内容...', '隐私政策', function () {
            // 可以在这里添加跳转到详细隐私政策页面的逻辑
            // window.open('/agreement/privacy-policy', '_blank');
        });
    }
</script>

</html>