<!DOCTYPE html>
<html lang="zh-CN" class="ui-page-login">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>绑定</title>
    <link rel="stylesheet" href="/ug/mall/css/mui.min.css">
    <link rel="stylesheet" href="/ug/mall/assest/css/iconfont.css">
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
        }
        
        .mui-content {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #F9F9F9;
        }
        
        .register-wrap {
            padding: 0;
            margin: 62px 0 40px;
        }
        
        .register-box {
            padding: 0 15px;
        }
        
        .mui-scroll-wrapper {
            top: 62px;
            margin-bottom: 176px;
        }
        
        .register-tips {
            color: #999;
            font-size: 14px;
        }
        
        .register-wrap .mall-input-row>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        .mui-input-row.mall-input-row input {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background-color: #F9F9F9;
        }
        
        .mui-input-row input::-webkit-input-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input::-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-ms-input-placeholder {
            color: #D6D4D4;
        }
        
        .mall-input-row>.mui-input-clear~.mui-icon-clear {
            top: 13px;
        }
        
        .btn-bind {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            border-radius: 24px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #637FFF;
            color: #fff;
        }
        
        .btn-register {
            text-align: center;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            margin-top: 22px;
            border-radius: 24px;
            text-align: center;
            border: 1px solid #637FFF;
            background-color: #F9F9F9;
            color: #637FFF;
        }
        
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        
        .register-box .desc-wrap {
            width: 100%;
            margin: 0 0 10px;
        }
        
        .register-box .desc-wrap p {
            font-size: 16px;
            color: #303133;
            font-weight: 700;
            line-height: 22px;
            margin: 0;
            padding: 0;
        }
        
        .register-box .mall-input-row {
            margin: 0;
            padding: 6px 0;
            background-color: #F9F9F9;
            box-shadow: 0px 1px 0px 0px #EAEAEA;
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
            height: 62px;
            line-height: 48px;
        }
        
        .mall-input-inner {
            -webkit-flex: 1;
            flex: 1;
        }
        
        .flex-box-align-center {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .flex-box-align-left {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: flex-start;
            justify-content: flex-start;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .register-box .mall-input-row>span,
        .mall-input-inner>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        #code {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background: #F9F9F9;
        }
        
        .get-code-box {
            width: 80px;
            height: 48px;
            line-height: 48px;
            text-align: center;
        }
        
        .get-code-box>input[type=button] {
            border: 0;
            color: #637FFF;
            height: inherit;
            line-height: inherit;
            font-size: 14px;
            padding: 0;
            font-weight: normal;
            width: 100%;
            border-radius: 0 10px 10px 0;
            background: #F9F9F9;
        }
        
        .bottom-btn {
            width: 100%;
            padding: 0 20px;
        }
        
        .accout-item {
            height: 80px;
            padding: 30px 0 10px;
            border-bottom: 1px solid #EAEAEA;
        }
        
        .item-info {
            flex: 1;
            line-height: 22px;
        }
        
        .item-info .item-name {
            font-size: 16px;
            color: #303133;
            margin: 0;
            padding: 0;
        }
        
        .item-info .item-name em {
            margin-left: 5px;
            font-size: 12px;
            color: #E7B021;
            font-style: normal;
        }
        
        .item-info .item-detail {
            font-size: 14px;
            color: #B3B3B3;
            margin: 3px 0 0;
            padding: 0;
        }
        
        .item-info .item-detail span {
            margin-right: 20px;
        }
        
        .radio-box {
            width: 37px;
            padding: 0 11px;
        }
        
        .mui-checkbox.mui-left input[type=checkbox],
        .mui-radio.mui-left input[type=radio] {
            width: 15px;
            height: 15px;
            left: 6px;
        }
        
        .mui-checkbox input[type=checkbox]:before,
        .mui-radio input[type=radio]:before {
            font-family: Muiicons;
            font-size: 18px;
        }
        
        .mui-checkbox input[type=checkbox]:checked:before,
        .mui-radio input[type=radio]:checked:before {
            color: #637FFF;
        }
    </style>

</head>

<body>
    <div class="mui-content mall-login">
        <div class="register-wrap">
            <div class="mui-scroll-wrapper">
                <div class="mui-scroll">
                    <div class="register-box">

                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-btn">
            <div class="mui-btn-primary btn-bind">绑定</div>
            <div class="mui-btn-primary btn-register">继续注册</div>
        </div>
    </div>
</body>
<script src="/ug/js/jquery.min.js"></script>
<script src="/ug/mall/js/mui.min.js"></script>
<script src="/ug/mall/assest/js/mui.utils.js"></script>
<script>
    var logininfo = "#{$logininfo}#";

    //-- beg
    logininfo = {
        userinfo: {
            mobile: 13221011234
        },
        table: {
            title: [],
            data: [{
                name: '张三',
                sex: '男',
                grade: '金卡会员',
                birthday: '1990-01-01'
            }, {
                name: '蒙娜丽莎',
                sex: '女',
                grade: '金卡会员',
                birthday: '1990-01-01'
            }, {
                name: '李二',
                sex: '男',
                grade: '银卡会员',
                birthday: '1990-01-01'
            }]
        }
    };
    //-- end
    var INDEX = 0;
    $(function() {
        htmlInfo();
        mui('.bottom-btn').on('tap', '.btn-bind', submitInfo);
        mui('.bottom-btn').on('tap', '.btn-register', function() {
            window.location.href = '{$url.registerinfo}' + '?phone=' + logininfo.userinfo.mobile;
        });
        mui('.register-box').on('tap', '.accout-item', function(event) {
            var no = $(this).attr('_no'),
                checked = $(this).find('input').prop('checked');
            if (no == INDEX) {
                if (event.target.type == 'checkbox') {
                    $(this).find('input').prop('checked', false);
                } else {
                    $(this).find('input').prop('checked', true);
                }
            } else {
                INDEX = no;
                if (event.target.type == 'checkbox') {} else {
                    $(this).find('input').prop('checked', !checked);
                }
                $(this).siblings('div.accout-item').each(function() {
                    $(this).find('input').prop('checked', false);
                });
            }
        });
    });

    function htmlInfo() {
        var html = '',
            userinfo = logininfo.userinfo,
            tableinfo = logininfo.table,
            i = 0;
        html += '<div class="desc-wrap">';
        html += '	<p>根据' + userinfo.mobile + '匹配到以下几个账号</p>';
        html += '	<p>请选择已创建的账号进行绑定或者继续注册</p>';
        html += '</div>';
        html += '<div class="list-wrap">';
        for (i = 0; i < tableinfo.data.length; i++) {
            var item = tableinfo.data[i];
            var birthday = item.birthday ? item.birthday.date('yyyy-MM-dd') : '--'
            html += '   <div class="accout-item flex-box-align-left" _no="' + i + '">';
            html += '		<div class="mui-input-row mui-checkbox mui-left radio-box ' + ((i == 0) ? 'checked' : '') + '">';
            html += '			<label></label>';
            html += '			<input class="hg-check-col flag-check-item" type="checkbox" value="">';
            html += '		</div>';
            html += '		<div class="item-info">';
            html += '		     <h5 class="item-name">' + item.name + '<em>' + item.grade + '</em></h5>';
            html += '		     <p class="item-detail"><span>' + item.sex + '</span><span>生日 ' + birthday + '</span></p>';
            html += '		</div>';
            html += '	</div>';
        }
        html += '</div>';
        $('.register-box').html(html);
        $('.register-box').find('.accout-item:first').find('input').prop('checked', true);

        var sHeight = $('.mui-content').height() - 62 - 176,
            listHeight = $('.register-box').height();
        if (listHeight < sHeight) {
            $('.register-wrap').height(listHeight);
            $('.mui-scroll-wrapper').height(listHeight);
        } else {
            $('.register-wrap').height(sHeight);
            mui('.mui-scroll-wrapper').scroll();
        }
    }

    function submitInfo() {
        var turl = "";
        var pdata = {
            id: logininfo.table.data[INDEX].id,
            uname: logininfo.table.data[INDEX].name,
            phone: logininfo.userinfo.mobile
        };
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                window.location.replace('{$url.redirect}');
            } else {
                mui.alert(jo.getMessage());
            }
        });
    }
</script>

</html>