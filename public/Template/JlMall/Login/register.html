<!DOCTYPE html>
<html lang="zh-CN" class="ui-page-login">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>注册</title>
    <link rel="stylesheet" href="/ug/mall/css/mui.min.css">
    <link rel="stylesheet" href="/ug/mall/assest/css/iconfont.css">
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
        }
        
        .mui-content {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #F9F9F9;
        }
        
        .register-wrap {
            padding: 0 15px;
        }
        
        .register-box {
            /*padding-bottom: 30px;*/
            padding: 76px 0 0;
        }
        
        .register-tips {
            color: #999;
            font-size: 14px;
        }
        
        .register-wrap .mall-input-row>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }
        
        .mui-input-row.mall-input-row input {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background-color: #F9F9F9;
        }
        
        .mui-input-row input::-webkit-input-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input::-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-moz-placeholder {
            color: #D6D4D4;
        }
        
        .mui-input-row input:-ms-input-placeholder {
            color: #D6D4D4;
        }
        
        .mall-input-row>.mui-input-clear~.mui-icon-clear {
            top: 13px;
        }
        
        .btn-register {
            text-align: center;
            margin-top: 41px;
            width: 100%;
            height: 48px;
            line-height: 48px;
            font-size: 16px;
            margin-top: 30px;
            border-radius: 24px;
            text-align: center;
            border: #637FFF;
            background-color: #637FFF;
            color: #fff;
        }
        
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
        
        .register-box h5.title {
            margin: 0 0 30px;
            font-size: 20px;
            color: #303133;
            font-weight: 700;
            line-height: 28px;
        }
        
        .register-box .mall-input-row {
            margin: 0;
            padding: 6px 0;
            background-color: #F9F9F9;
            box-shadow: 0px 1px 0px 0px #EAEAEA;
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
            height: 62px;
            line-height: 48px;
        }
        
        .mall-input-inner {
            -webkit-flex: 1;
            flex: 1;
        }
        
        .flex-box-align-center {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-align-items: center;
            align-items: center;
        }
        
        .register-box .mall-input-row>span,
        .mall-input-inner>span {
            width: 40px;
            text-align: center;
            font-size: 18px;
        }

        .btn-login{
            text-align: center;
        }

        .btn-login{
            margin-top: 20px;
        }

        .btn-login>a {
            padding: 5px 6px;
            font-size: 16px;
            color: #7D95FF;
        }
        
        #code {
            -webkit-flex: 1;
            flex: 1;
            margin-bottom: 0;
            border: 0;
            font-size: 14px;
            padding-left: 0;
            text-align: left;
            padding: 0 12px 0 0;
            background: #F9F9F9;
        }
        
        .get-code-box {
            width: 80px;
            height: 48px;
            line-height: 48px;
            text-align: center;
        }
        
        .get-code-box>input[type=button] {
            border: 0;
            color: #637FFF;
            height: inherit;
            line-height: inherit;
            font-size: 14px;
            padding: 0;
            font-weight: normal;
            width: 100%;
            border-radius: 0 10px 10px 0;
            background: #F9F9F9;
        }
        
        .bottom-btn {
            width: 100%;
            padding: 0 20px;
        }
    </style>

</head>

<body>
    <div class="mui-content mall-login">
        <div class="register-wrap">
            <div class="register-box">
                <h5 class="title">注册</h5>
                <div class="mui-input-row mall-input-row">
                    <input id="mobile" class="mui-input-clear mui-input" type="number" pattern="[0-9]*" placeholder="请输入手机号">
                </div>
                <div class="mall-input-row">
                    <div class="mui-input-row flex-box-align-center mall-input-inner">
                        <input id="code" class="mui-input-clear mui-input" type="text" placeholder="请输入验证码">
                    </div>
                    <div class="get-code-box">
                        <input class="btn-get-code" type="button" value="获取验证码" />
                    </div>
                </div>
            </div>
            <div class="bottom-btn">
                <div class="mui-btn-primary btn-register">下一步</div>
                <div class="btn-login">
                    <a href="{$url.login}">去登录</a>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="/ug/js/jquery.min.js"></script>
<script src="/ug/mall/js/mui.min.js"></script>
<script src="/ug/mall/assest/js/mui.utils.js"></script>
<script>
    $(function() {
        mui('.register-wrap').on('tap', '.btn-register', registerInfo);

        $('.register-box input').on('blur', function() {
            document.activeElement.blur();
        });
        mui('.register-box').on('tap', '#mobile', function() {
            var u = navigator.userAgent;
            var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
            if (isAndroid == true) {
                $('#mobile').attr('pattern', '\d');
                $('#mobile').attr('type', "text");
            }
        });
        $('#mobile').on('keydown', function(e) {
            if (e.keyCode == 13) {
                document.activeElement.blur();
            }
        });
        mui('.mall-login').on('tap', '.btn-login>a', function() {
            window.location.href = this.href;
        });
        mui('.register-wrap').on('tap', '.get-code-box > .btn-get-code', setCode);
    });

    function registerInfo() {
        document.activeElement.blur();
        var mobile = $('#mobile').val(),
            code = $('#code').val();

        /*验证用户信息是否为空*/
        if (!check({
                // account: account,
                mobile: mobile,
                // birthday: birthday,
                code: code,
            })) return false;
        var turl = "";
        var pdata = {
            // c0: account,
            phone: mobile,
            // c2: birthday,
            ver_code: code
        };
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo._c == 1) {
                window.location.href = '{$url.registerinfo}' + '&phone=' + mobile;
            } else if (jo._c == 2) {
                window.location.href = '{$url.registerlist}' + '&phone=' + mobile;
            } else {
                mui.toast(jo._m);
            }
        });

    }
    var wait = 60;

    function time(btn) {
        if (wait == 0) {
            btn.removeClass("bg-gray");
            btn.removeAttr("disabled");
            btn.val("获取验证码");
            wait = 60;
        } else {
            btn.addClass("bg-gray");
            btn.attr("disabled", true);
            btn.val(wait + "秒重发");
            wait--;
            setTimeout(function() {
                time(btn);
            }, 1000);
        }
    };

    function isEmpty(val) {
        return val === ''
    };

    function check_userName(account) {
        var _max = 8;
        var _cur = getByteLen(account);
        if (_cur > _max) {
            return false;
        }
        return true;
    };

    function getByteLen(val) { // 返回val的字节长度
        var len = 0;
        for (var i = 0; i < val.length; i++) {
            /*if(val[i].match(/[^\x00-\xff]/ig) != null) //全角
            	len += 2;
            else*/
            len += 1;
        }
        return len;
    };

    function check(obj) {
        if (isEmpty(obj.account)) {
            mui.toast('用户名不能为空');
            return false;
        };
        //				if(!check_userName(obj.account)) {
        //					mui.toast('用户名过长');
        //					return false;
        //				};
        if (isEmpty(obj.mobile)) {
            mui.toast('手机号不能为空');
            return false;
        };
        if (!isValidPhone(obj.mobile)) {
            mui.toast('请填写正确的手机号');
            return false;
        }
        if (isEmpty(obj.code)) {
            mui.toast('验证码不能为空');
            return false;
        }
        return true
    };

    function isValidPhone(val) { //手机号校验
        return /^1[0-9]{10}$/.test(val);
    };

    function setCode() {
        document.activeElement.blur();
        var mobile = $('#mobile').val();
        /*验证用户信息是否为空*/
        if (isEmpty(mobile)) {
            mui.toast('手机号不能为空');
            return false;
        };
        if (!isValidPhone(mobile)) {
            mui.toast('请填写正确的手机号');
            return false;
        }
        var turl = '{$url.getcode}';
        var pdata = {
            'mobile': mobile
        }
        time($('.btn-get-code'));
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                mui.toast(jo.getMessage());
            } else {
                mui.alert(jo.getMessage());
            }
        });
    };
</script>

</html>